import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

export const supabase = createClient(supabaseUrl, supabaseAnonKey)

// Database types for TypeScript
export type Database = {
  public: {
    Tables: {
      profiles: {
        Row: {
          id: string
          email: string | null
          full_name: string | null
          avatar_url: string | null
          subscription_tier: 'free' | 'pro' | 'enterprise'
          credits_remaining: number
          total_apps_created: number
          created_at: string
          updated_at: string
        }
        Insert: {
          id: string
          email?: string | null
          full_name?: string | null
          avatar_url?: string | null
          subscription_tier?: 'free' | 'pro' | 'enterprise'
          credits_remaining?: number
          total_apps_created?: number
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          email?: string | null
          full_name?: string | null
          avatar_url?: string | null
          subscription_tier?: 'free' | 'pro' | 'enterprise'
          credits_remaining?: number
          total_apps_created?: number
          created_at?: string
          updated_at?: string
        }
      }
      projects: {
        Row: {
          id: string
          user_id: string
          name: string
          description: string
          app_type: 'web' | 'mobile' | 'desktop'
          status: 'draft' | 'generating' | 'completed' | 'failed' | 'deployed'
          original_description: string
          processed_requirements: any | null
          tech_stack: any | null
          features: any | null
          design_preferences: any | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          name: string
          description: string
          app_type?: 'web' | 'mobile' | 'desktop'
          status?: 'draft' | 'generating' | 'completed' | 'failed' | 'deployed'
          original_description: string
          processed_requirements?: any | null
          tech_stack?: any | null
          features?: any | null
          design_preferences?: any | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          name?: string
          description?: string
          app_type?: 'web' | 'mobile' | 'desktop'
          status?: 'draft' | 'generating' | 'completed' | 'failed' | 'deployed'
          original_description?: string
          processed_requirements?: any | null
          tech_stack?: any | null
          features?: any | null
          design_preferences?: any | null
          created_at?: string
          updated_at?: string
        }
      }
      generated_apps: {
        Row: {
          id: string
          project_id: string
          version: number
          frontend_code: any | null
          backend_code: any | null
          database_schema: any | null
          assets: any | null
          package_json: any | null
          readme_content: string | null
          build_status: 'pending' | 'building' | 'success' | 'failed'
          build_logs: string | null
          preview_url: string | null
          source_code_url: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          project_id: string
          version?: number
          frontend_code?: any | null
          backend_code?: any | null
          database_schema?: any | null
          assets?: any | null
          package_json?: any | null
          readme_content?: string | null
          build_status?: 'pending' | 'building' | 'success' | 'failed'
          build_logs?: string | null
          preview_url?: string | null
          source_code_url?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          project_id?: string
          version?: number
          frontend_code?: any | null
          backend_code?: any | null
          database_schema?: any | null
          assets?: any | null
          package_json?: any | null
          readme_content?: string | null
          build_status?: 'pending' | 'building' | 'success' | 'failed'
          build_logs?: string | null
          preview_url?: string | null
          source_code_url?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      deployments: {
        Row: {
          id: string
          project_id: string
          generated_app_id: string
          deployment_url: string | null
          deployment_status: 'pending' | 'deploying' | 'deployed' | 'failed'
          deployment_platform: 'vercel' | 'netlify' | 'heroku' | 'aws'
          deployment_logs: string | null
          environment_variables: any | null
          custom_domain: string | null
          ssl_enabled: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          project_id: string
          generated_app_id: string
          deployment_url?: string | null
          deployment_status?: 'pending' | 'deploying' | 'deployed' | 'failed'
          deployment_platform?: 'vercel' | 'netlify' | 'heroku' | 'aws'
          deployment_logs?: string | null
          environment_variables?: any | null
          custom_domain?: string | null
          ssl_enabled?: boolean
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          project_id?: string
          generated_app_id?: string
          deployment_url?: string | null
          deployment_status?: 'pending' | 'deploying' | 'deployed' | 'failed'
          deployment_platform?: 'vercel' | 'netlify' | 'heroku' | 'aws'
          deployment_logs?: string | null
          environment_variables?: any | null
          custom_domain?: string | null
          ssl_enabled?: boolean
          created_at?: string
          updated_at?: string
        }
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      get_user_dashboard: {
        Args: {
          user_uuid: string
        }
        Returns: {
          total_projects: number
          completed_projects: number
          active_projects: number
          total_deployments: number
          credits_remaining: number
        }[]
      }
      get_user_token_usage: {
        Args: {
          user_uuid: string
          start_date?: string
        }
        Returns: {
          total_tokens: number
          total_cost: number
        }[]
      }
      get_project_details: {
        Args: {
          project_uuid: string
        }
        Returns: {
          project_data: any
          generated_app_data: any
          deployment_data: any
          generation_progress: any
        }[]
      }
    }
    Enums: {
      [_ in never]: never
    }
  }
}
