import { supabase } from './supabase'
import type { Database } from './supabase'

type Project = Database['public']['Tables']['projects']['Row']
type ProjectInsert = Database['public']['Tables']['projects']['Insert']
type ProjectUpdate = Database['public']['Tables']['projects']['Update']

export const projects = {
  // Get all projects for a user
  getUserProjects: async (userId: string) => {
    const { data, error } = await supabase
      .from('projects')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })

    return { data, error }
  },

  // Get a single project by ID
  getProject: async (projectId: string) => {
    const { data, error } = await supabase
      .from('projects')
      .select('*')
      .eq('id', projectId)
      .single()

    return { data, error }
  },

  // Create a new project
  createProject: async (project: ProjectInsert) => {
    const { data, error } = await supabase
      .from('projects')
      .insert(project)
      .select()
      .single()

    return { data, error }
  },

  // Update a project
  updateProject: async (projectId: string, updates: ProjectUpdate) => {
    const { data, error } = await supabase
      .from('projects')
      .update(updates)
      .eq('id', projectId)
      .select()
      .single()

    return { data, error }
  },

  // Delete a project
  deleteProject: async (projectId: string) => {
    const { error } = await supabase
      .from('projects')
      .delete()
      .eq('id', projectId)

    return { error }
  },

  // Get project with all related data
  getProjectDetails: async (projectId: string) => {
    const { data, error } = await supabase
      .rpc('get_project_details', { project_uuid: projectId })

    return { data: data?.[0] || null, error }
  },

  // Update project status
  updateProjectStatus: async (
    projectId: string, 
    status: 'draft' | 'generating' | 'completed' | 'failed' | 'deployed'
  ) => {
    const { data, error } = await supabase
      .from('projects')
      .update({ status })
      .eq('id', projectId)
      .select()
      .single()

    return { data, error }
  },
}

// Generated Apps functions
export const generatedApps = {
  // Get generated app for a project
  getGeneratedApp: async (projectId: string) => {
    const { data, error } = await supabase
      .from('generated_apps')
      .select('*')
      .eq('project_id', projectId)
      .order('version', { ascending: false })
      .limit(1)
      .single()

    return { data, error }
  },

  // Create a new generated app
  createGeneratedApp: async (generatedApp: Database['public']['Tables']['generated_apps']['Insert']) => {
    const { data, error } = await supabase
      .from('generated_apps')
      .insert(generatedApp)
      .select()
      .single()

    return { data, error }
  },

  // Update generated app
  updateGeneratedApp: async (
    generatedAppId: string, 
    updates: Database['public']['Tables']['generated_apps']['Update']
  ) => {
    const { data, error } = await supabase
      .from('generated_apps')
      .update(updates)
      .eq('id', generatedAppId)
      .select()
      .single()

    return { data, error }
  },
}

// Deployments functions
export const deployments = {
  // Get deployments for a project
  getProjectDeployments: async (projectId: string) => {
    const { data, error } = await supabase
      .from('deployments')
      .select('*')
      .eq('project_id', projectId)
      .order('created_at', { ascending: false })

    return { data, error }
  },

  // Create a new deployment
  createDeployment: async (deployment: Database['public']['Tables']['deployments']['Insert']) => {
    const { data, error } = await supabase
      .from('deployments')
      .insert(deployment)
      .select()
      .single()

    return { data, error }
  },

  // Update deployment status
  updateDeploymentStatus: async (
    deploymentId: string,
    status: 'pending' | 'deploying' | 'deployed' | 'failed',
    deploymentUrl?: string,
    logs?: string
  ) => {
    const updates: any = { deployment_status: status }
    if (deploymentUrl) updates.deployment_url = deploymentUrl
    if (logs) updates.deployment_logs = logs

    const { data, error } = await supabase
      .from('deployments')
      .update(updates)
      .eq('id', deploymentId)
      .select()
      .single()

    return { data, error }
  },
}

// Generation logs functions
export const generationLogs = {
  // Add a generation log entry
  addLog: async (log: Database['public']['Tables']['generation_logs']['Insert']) => {
    const { data, error } = await supabase
      .from('generation_logs')
      .insert(log)
      .select()
      .single()

    return { data, error }
  },

  // Get logs for a project
  getProjectLogs: async (projectId: string) => {
    const { data, error } = await supabase
      .from('generation_logs')
      .select('*')
      .eq('project_id', projectId)
      .order('created_at', { ascending: true })

    return { data, error }
  },

  // Update log status
  updateLogStatus: async (
    logId: string,
    status: 'running' | 'completed' | 'failed' | 'skipped',
    outputData?: any,
    errorMessage?: string,
    executionTimeMs?: number
  ) => {
    const updates: any = { 
      status,
      completed_at: new Date().toISOString()
    }
    if (outputData) updates.output_data = outputData
    if (errorMessage) updates.error_message = errorMessage
    if (executionTimeMs) updates.execution_time_ms = executionTimeMs

    const { data, error } = await supabase
      .from('generation_logs')
      .update(updates)
      .eq('id', logId)
      .select()
      .single()

    return { data, error }
  },
}
