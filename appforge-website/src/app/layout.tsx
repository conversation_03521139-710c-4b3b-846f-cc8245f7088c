import type { Metada<PERSON> } from "next";
import "./globals.css";
import { AuthProvider } from "@/contexts/AuthContext";

export const metadata: Metadata = {
  title: "AppForge - Transform Ideas into Web Apps with AI",
  description: "AppForge is an AI-powered platform that transforms natural language descriptions into fully-functional web applications. No coding required - turn your ideas into working applications in minutes.",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body className="antialiased">
        <AuthProvider>
          {children}
        </AuthProvider>
      </body>
    </html>
  );
}
